package com.nercar.contract.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.PageDataResult;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.domain.ContractInfoDomain;
import com.nercar.contract.domain.PageDataInfo;
import com.nercar.contract.dto.FinalOpinionDTO;
import com.nercar.contract.dto.TechCentStdSectHomePendingParam;
import com.nercar.contract.dto.TechCentStdSectHomeReviewedParam;
import com.nercar.contract.entity.*;
import com.nercar.contract.enums.StepEnum;
import com.nercar.contract.mapper.*;
import com.nercar.contract.service.IContractInfoService;
import com.nercar.contract.service.IOverallOpinionService;
import com.nercar.contract.service.IReviewCommentService;
import com.nercar.contract.service.ISpecificationBaseService;
import com.nercar.contract.service.ISpecificationInfoService;
import com.nercar.contract.service.ProcessFlowService;
import com.nercar.contract.utils.TypeSafeUtils;
import com.nercar.contract.validator.ValidationResult;
import com.nercar.contract.validator.ValidatorImpl;
import com.nercar.contract.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @description: 技术中心标注科首页
 * @author: zmc
 * @date: 2024/10/11 21:43
 */
@Api(tags = "技术中心标准科")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/techCenterStandardSection")
public class TechCentStdSectController {

    private final IContractInfoService contractInfoService;
    private final IOverallOpinionService overallOpinionService;
    private final ValidatorImpl validator;
    private final ReviewCommentMapper reviewCommentMapper;
    private final ReviewInfoMapper reviewInfoMapper;
    private final ReviewCommentInfoMapper reviewCommentInfoMapper;
    private final ContractReviewCommentMapper contractReviewCommentMapper;
    private final ProcessFlowService processFlowService;
    private final EmployeeMapper employeeMapper;
    private final ContractInfoMapper contractInfoMapper;
    private final OrganizationMapper organizationMapper;
    private final ISpecificationBaseService specificationBaseService;
    private final ISpecificationInfoService specificationInfoService;
    private final IReviewCommentService reviewCommentService;

    @ApiOperation("技术中心标准科-待审核订单列表")
    @PostMapping("/getPendingOrders")
    public PageDataResult<TechCentStdPendingOrdersVO> getPendingOrders(@RequestBody TechCentStdSectHomePendingParam param) throws BusinessException {
        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getTechCentStdCentPendingOrders(param);
        List<TechCentStdPendingOrdersVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), TechCentStdPendingOrdersVO.class);
        for (TechCentStdPendingOrdersVO re : res) {
            String[] split = re.getValue1() != null ? re.getValue1().split(",") : new String[0];
            re.setNorm(split);
        }
        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("技术中心标准科-已审核订单")
    @PostMapping("/getReviewedOrders")
    public PageDataResult<TechCentStdReviewedVO> getReviewedOrders(@RequestBody TechCentStdSectHomeReviewedParam param) throws BusinessException {
        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getTechCentStdReviewedOrders(param);
        List<TechCentStdReviewedVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), TechCentStdReviewedVO.class);
        for (TechCentStdReviewedVO infoDomain : res) {
            infoDomain.setReviewTime(processFlowService.getReviewTime(Long.valueOf(infoDomain.getId()), StepEnum.STANDARD_SUBMIT));
            List<ProcessFlow> list = processFlowService.list(new LambdaQueryWrapper<ProcessFlow>().eq(ProcessFlow::getContractInfoId, Long.valueOf(infoDomain.getId())).orderByDesc(ProcessFlow::getCreateTime));
            if ( list.get(0).getCurrentStep() == StepEnum.REEVALUATION) {
                infoDomain.setValue2("0");//流程走完了 可以复评
            }else {
                infoDomain.setValue2("1");//流程未走完 不可以复评
            }
            Employee employee = employeeMapper.selectById(infoDomain.getDirector());
            Organization organization = organizationMapper.selectById(employee.getOrganizationId());

            infoDomain.setFlow(organization.getOrganizationName());
        }
        if (param.getAuditTime()!=null&&!param.getAuditTime().isEmpty())
        {
            LocalDate localDate = LocalDate.parse(param.getAuditTime(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDateTime localDateTime = localDate.atStartOfDay(); // 或者也可以用 .atTime(0, 0)
             res.removeIf(a -> a.getReviewTime() == null || !a.getReviewTime().toLocalDate().isEqual(localDate));
        }

        // 将 LocalDate 转换为 LocalDateTime，默认时间为午夜 (00:00)

        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("技术中心标准科-退回销售")
    @PostMapping("/returnSell")
    public CommonResult<Boolean> returnSell(@ApiParam(value = "{\"contractInfoId\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        if (map == null || map.isEmpty() || Objects.isNull(map.get("id"))) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数为空");
        }
        // 修复PostgreSQL类型不匹配问题：将String类型ID转换为Long类型
        Long contractId = TypeSafeUtils.safeParseLong(map.get("id"));
        if (contractId == null) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "无效的合同ID格式");
        }
        ContractInfo contractInfo = contractInfoService.getById(contractId);
        contractInfo.setIsSubmit(0);
        contractInfo.setSubmitTime(null);
        contractInfo.setItemId(1);
        contractInfo.setStatusId(1);
        contractInfo.setReturnReason(map.get("returnReason"));
        contractInfo.setReviewStatus(3); // 设置为被驳回状态
        processFlowService.insert(contractInfo.getId(), StepEnum.STANDARD_RETURN);
        boolean b = contractInfoService.updateById(contractInfo);
        return CommonResult.success(b);
    }

    @ApiOperation("技术中心标准科-待审核订单详情")
    @PostMapping("/getReviewedOrderInfo")
    public CommonResult<TechCentStdPendingOrderInfo> getPendingOrderInfo(@ApiParam(value = "{\"contractInfoId\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        if (map == null || map.isEmpty() || Objects.isNull(map.get("contractInfoId"))) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数为空");
        }

        Long contractInfoId = Long.valueOf(map.get("contractInfoId"));

        ContractInfoDomain domain = contractInfoService.getContractInfoById(contractInfoId);
        if (domain == null) {
            return CommonResult.failed("该订单不存在");
        }
        TechCentStdPendingOrderInfo info = BeanUtil.copyProperties(domain, TechCentStdPendingOrderInfo.class);

        // 拼接规格、具体尺寸和规格备注
        String displaySpecification = formatSpecificationDisplay(domain);
        info.setSpecification(displaySpecification);

        info.setIsProduce(domain.getIsProduce().equals(1));
        info.setIsCostCalculation(domain.getIsCostCalculation().equals(1));
        info.setIsOutsourcingFirm(domain.getIsOutsourcingFirm().equals(1));

        // 查询科室主任的评审意见
        ReviewComment reviewComment = reviewCommentService.getReviewOpinionById(contractInfoId.toString());
        if (reviewComment != null) {
            info.setReceivingState(reviewComment.getReceivingState());
            info.setReceivingStateText(getReceivingStateText(reviewComment.getReceivingState()));
            info.setReceivingRemark(reviewComment.getReceivingRemark());
            info.setCostCalculation(reviewComment.getCostCalculation());
            info.setAssess(reviewComment.getAssess());
            info.setAssessText(getAssessText(reviewComment.getAssess()));
            info.setIsMakeReview(reviewComment.getIsMake());
            info.setIsMakeReviewText(getIsMakeText(reviewComment.getIsMake()));
            info.setOutsourcingStatusReview(reviewComment.getOutsourcingStatus());
            info.setIsCostByChange(reviewComment.getIsCostByChange());
            info.setIsCostByChangeText(getIsCostByChangeText(reviewComment.getIsCostByChange()));
        }

        return CommonResult.success(info);
    }

    @ApiOperation("技术中心标准科-修改意见订单记录-修改详情")
    @PostMapping("/updateOrderOpinion")
    public CommonResult<HashMap<String, ReviewCommentAndInfoVo>> updateOrderOpinion(@ApiParam(value = "{\"id\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        //        主表 ->合同评审意见中间表  一对多
        //        评审意见表中间表 ->  评审意见表  一对一
        //        这时候有启用弃用字段   可以判断是否最新记录  启用是最新
        //        评审意见 -> 评审意见信息中间表    一对多

        String id = map.get("id");
        if (Objects.isNull(id)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "contractInfoId不能为空");
        }
        Long contractInfoId = Long.valueOf(id);

        List<ContractReviewComment> contractReviewComments = contractReviewCommentMapper.selectList(new LambdaQueryWrapper<ContractReviewComment>().eq(ContractReviewComment::getContractInfoId, contractInfoId));
        HashMap<String, ReviewCommentAndInfoVo> reviewCommentAndInfoVos = new HashMap<>();
        //主任意见
        ContractInfo contractInfo = contractInfoService.getById(contractInfoId);
        OverallOpinion byId1 = null;
        if (contractInfo != null && contractInfo.getOverallOpinionId() != null) {
            byId1 = overallOpinionService.getById(contractInfo.getOverallOpinionId());
        }
        //两条新旧评审意见
        for (ContractReviewComment item : contractReviewComments) {


            ReviewCommentAndInfoVo reviewCommentAndInfoVo = new ReviewCommentAndInfoVo();
            Long reviewCommentId = item.getReviewCommentId();
            //评审意见
            ReviewComment reviewComment = reviewCommentMapper.selectById(reviewCommentId);
            BeanUtils.copyProperties(reviewComment, reviewCommentAndInfoVo);
            //评审信息
            List<ReviewCommentInfo> reviewCommentInfos = reviewCommentInfoMapper.selectList(new LambdaQueryWrapper<ReviewCommentInfo>().eq(ReviewCommentInfo::getReviewCommentId, reviewComment.getId()));
            Iterator<ReviewCommentInfo> iterator = reviewCommentInfos.iterator();
            ArrayList<Long> ids = new ArrayList<>();
            while (iterator.hasNext()) {
                ReviewCommentInfo next = iterator.next();
                ids.add(next.getReviewInfoId());
            }
            List<ReviewInfo> reviewInfos = reviewInfoMapper.selectBatchIds(ids);
            reviewCommentAndInfoVo.setReviewInfos(reviewInfos);
            if (reviewComment.getIsUse() == 1) {
                reviewCommentAndInfoVo.setRemark(byId1 != null ? byId1.getRemarkMount() : "");
                reviewCommentAndInfoVos.put("new", reviewCommentAndInfoVo);
            } else {
                reviewCommentAndInfoVo.setRemark(byId1 != null ? byId1.getRemark() : "");
                reviewCommentAndInfoVos.put("old", reviewCommentAndInfoVo);
            }
        };
        ReviewCommentAndInfoVo reviewCommentAndInfoVo = reviewCommentAndInfoVos.get("new");
        List<ReviewInfo> reviewInfos = reviewCommentAndInfoVo.getReviewInfos();
        reviewInfos.forEach(item -> {
            if (!item.getReviewComment().equals(item.getCommentModified())) {
                item.setCommentModified("<em>" + item.getCommentModified() + "<em>");
            }
        });

        return CommonResult.success(reviewCommentAndInfoVos);
    }

    @ApiOperation("技术中心标准科-待审核订单-修改意见订单记录")
    @PostMapping("/getOrderOpinionRecord")
    public PageDataResult<TechCentStdReviewedVO> updateOrderOpinion(@RequestBody TechCentStdSectHomeReviewedParam param) throws BusinessException {
        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getOrderOpinionRecord(param);

        List<TechCentStdReviewedVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), TechCentStdReviewedVO.class);

        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("最终评审意见-通过")
    @PostMapping("/finalOpinionApprove")
    public CommonResult<Boolean> finalOpinionApprove(@RequestBody FinalOpinionDTO finalOpinionDTO) throws BusinessException {
        ValidationResult result = this.validator.validate(finalOpinionDTO);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        return CommonResult.success(contractInfoService.finalOpinionApprove(finalOpinionDTO));
    }

    @ApiOperation("最终评审意见-不通过")
    @PostMapping("/finalOpinionReject")
    public CommonResult<Boolean> finalOpinionReject(@RequestBody FinalOpinionDTO finalOpinionDTO) throws BusinessException {
        ValidationResult result = this.validator.validate(finalOpinionDTO);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        return CommonResult.success(contractInfoService.finalOpinionReject(finalOpinionDTO));
    }

    @ApiOperation("订单复评")
    @PostMapping("/reReview")
    public CommonResult<Boolean> reReview(@ApiParam(value = "{\"contractInfoIdList\":\"\"}") @RequestBody Map<String, List<String>> map) throws BusinessException {
        if (map == null || map.isEmpty() || Objects.isNull(map.get("contractInfoIdList"))) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数为空");
        }
        List<String> ids = map.get("contractInfoIdList");
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数为空");
        }
        return CommonResult.success(contractInfoService.reReview(ids));
    }

    @ApiOperation("分发订单")
    @PostMapping("/distributeOrders")
    public CommonResult<Boolean> distributeOrders(@ApiParam(value = "传人员id及合同表contractInfoId{\"id\":\"\",\"contractInfoId\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        //分发选择主任副主任 同部门下评审人员随便看   主任的话只能看见自己的
        String id = map != null && !map.get("id").isEmpty() ? map.get("id") : null;
        String contractInfoId = map != null && !map.get("contractInfoId").isEmpty() ? map.get("contractInfoId") : null;
        if (id == null || contractInfoId == null) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数为空");
        }
        ContractInfo contractInfo = contractInfoMapper.selectById(Long.valueOf(contractInfoId));
        contractInfo.setDirector(id);
        contractInfoMapper.updateById(contractInfo);
        processFlowService.insert(Long.valueOf(contractInfoId), StepEnum.STANDARD_SUBMIT);
        return CommonResult.success();
    }

    @ApiOperation("获取流程信息")
    @PostMapping("/getFlowInfo")
    public CommonResult<OrderFlow> getFlowInfo(@ApiParam(value = "{\"contractInfoId\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        if (map == null || map.isEmpty() || Objects.isNull(map.get("contractInfoId"))) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数为空");
        }

        Long contractInfoId = Long.valueOf(map.get("contractInfoId"));
        return CommonResult.success(contractInfoService.getOrderFlowInfo(contractInfoId));
    }

    /**
     * 获取接单状态文字描述
     * @param receivingState 接单状态
     * @return 状态描述
     */
    private String getReceivingStateText(Integer receivingState) {
        if (receivingState == null) return null;
        switch (receivingState) {
            case 0: return "拒单";
            case 1: return "接单";
            case 2: return "条件接单";
            default: return "未知状态";
        }
    }

    /**
     * 获取风险等级评估文字描述
     * @param assess 风险等级评估
     * @return 评估描述
     */
    private String getAssessText(Integer assess) {
        if (assess == null) return null;
        switch (assess) {
            case 0: return "低风险";
            case 1: return "中风险";
            case 2: return "高风险";
            default: return "未知风险";
        }
    }

    /**
     * 获取首试制文字描述
     * @param isMake 首试制
     * @return 首试制描述
     */
    private String getIsMakeText(Integer isMake) {
        if (isMake == null) return null;
        switch (isMake) {
            case 0: return "否";
            case 1: return "是";
            default: return "未知";
        }
    }

    /**
     * 获取是否引起成本变化文字描述
     * @param isCostByChange 是否引起成本变化
     * @return 成本变化描述
     */
    private String getIsCostByChangeText(Long isCostByChange) {
        if (isCostByChange == null) return null;
        switch (isCostByChange.intValue()) {
            case 0: return "否";
            case 1: return "是";
            default: return "未知";
        }
    }

    /**
     * 格式化规格显示名称
     * @param domain 合同信息域对象
     * @return 拼接后的显示名称
     */
    private String formatSpecificationDisplay(ContractInfoDomain domain) {
        StringBuilder result = new StringBuilder(domain.getSpecification());

        // 获取具体尺寸信息和维度信息
        if (domain.getValue1() != null && !domain.getValue1().isEmpty() && domain.getSteelSpecificationId() != null) {
            try {
                // 根据steelSpecificationId查询SpecificationInfo，然后获取SpecificationBase的button信息
                SpecificationInfo specInfo = specificationInfoService.getById(domain.getSteelSpecificationId());
                if (specInfo != null && specInfo.getSpecificationId() != null) {
                    SpecificationBase specBase = specificationBaseService.getById(specInfo.getSpecificationId());
                    if (specBase != null && specBase.getButton() != null && !specBase.getButton().isEmpty()) {
                        // 使用完整的拼接逻辑
                        String[] dimensions = specBase.getButton().split(",");
                        String[] values = domain.getValue1().split(",");

                        result.append(":");
                        for (int i = 0; i < Math.min(dimensions.length, values.length); i++) {
                            if (i > 0) {
                                result.append("*");  // 添加维度分隔符
                            }
                            result.append(dimensions[i]).append(values[i]).append("mm");
                        }
                    } else {
                        // 如果没有维度信息，使用简单拼接
                        result.append(" ").append(domain.getValue1());
                    }
                } else {
                    // 如果查询不到规格信息，使用简单拼接
                    result.append(" ").append(domain.getValue1());
                }
            } catch (Exception e) {
                // 如果查询失败，使用简单拼接
                log.warn("Failed to format specification display for domain: {}", domain.getId(), e);
                result.append(" ").append(domain.getValue1());
            }
        }

        // 添加规格备注
        if (domain.getSpecificationNote() != null && !domain.getSpecificationNote().isEmpty()) {
            result.append("（").append(domain.getSpecificationNote()).append("）");
        }

        return result.toString();
    }
}
