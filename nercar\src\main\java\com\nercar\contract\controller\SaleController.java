package com.nercar.contract.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.nercar.contract.filter.RequestContextHolder;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.PageDataResult;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.domain.ContractInfoDomain;
import com.nercar.contract.domain.PageDataInfo;
import com.nercar.contract.dto.*;
import com.nercar.contract.entity.*;
import com.nercar.contract.enums.StepEnum;
import com.nercar.contract.mapper.StatusBaseMapper;
import com.nercar.contract.mapper.EmployeeMapper;
import com.nercar.contract.service.*;
import com.nercar.contract.utils.WordTableTextReplacer;
import com.nercar.contract.validator.ValidationResult;
import com.nercar.contract.validator.ValidatorImpl;
import com.nercar.contract.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;

import java.util.Objects;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ContentDisposition;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.zip.ZipOutputStream;
import java.util.zip.ZipEntry;


import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.time.format.DateTimeFormatter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.ArrayList;

/**
 * @description: 销售公司-首页
 * @author: zmc
 * @date: 2024/9/20
 */
@Api(tags = "销售公司")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sale")
public class SaleController {
    private final ISpecificationInfoService specificationInfoService;
    private final IFinalOpinionService finalOpinionService;
    private final IContractInfoService contractInfoService;
    private final ISpecificationBaseService specificationBaseService;
    private final IReviewInfoService reviewInfoService;
    private final IOverallOpinionService overallOpinionService;
    private final IReviewCommentService reviewCommentService;

    private final ICustomerInfoService customerInfoService;

    private final ISteelGradeBaseService steelGradeBaseService;
    private final ISteelTypeBaseService steelTypeBaseService;
    private final IItemBaseService itemBaseService;
    private final IReviewTypeService reviewTypeService;

    private final ITechnicalStandardBaseService technicalStandardBaseService;

    private final IOutsourcingService outsourcingService;

    private final IStandardBaseService standardBaseService;

    private final IDeliveryStatusBaseService deliveryStatusBaseService;

    private final IProcessingPurposeBaseService processingPurposeBaseService;

    private final ValidatorImpl validator;
    private final UserService userService;

    private final StatusBaseMapper statusBaseMapper;
    private final EmployeeMapper employeeMapper;
    private final IAuditService auditService;
    private final ProcessFlowService processFlowService;

    @org.springframework.beans.factory.annotation.Autowired
    private org.springframework.core.env.Environment env;

    @org.springframework.beans.factory.annotation.Autowired
    private io.minio.MinioClient minioClient;

    @org.springframework.beans.factory.annotation.Autowired
    private com.nercar.contract.config.MinioConfig minioConfig;

    // 合同模板配置
    @Value("${contract.template.base-path}")
    private String templateBasePath;

    @Value("${contract.template.review-template}")
    private String reviewTemplateName;

    @Value("${file.preview-url}")
    private String filePreviewUrl;

    @Value("${file.server-url:}")
    private String configuredServerUrl;

    private String serverUrl;

    @ApiOperation("待处理评审-核定外委业务-提交")
    @PostMapping("/submitOutsourcing")
    public CommonResult<Boolean> submitOutsourcing(@RequestBody OutsourcingDTO outsourcingDTO) throws BusinessException {
        if (Objects.isNull(outsourcingDTO)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数不能为空");
        }
        boolean res = contractInfoService.submitOutsourcing(outsourcingDTO);
        return CommonResult.success(true);
    }

    @ApiOperation("待处理评审-核定外委业务-保存")
    @PostMapping("/saveOutsourcing")
    public CommonResult<Boolean> saveOutsourcing(@RequestBody OutsourcingDTO outsourcingDTO) throws BusinessException {

        boolean res = contractInfoService.saveOrUpdateOutsourcing(outsourcingDTO);
        return CommonResult.success(true);
    }

    @ApiOperation("发起合同评审-保存")
    @PostMapping("/saveContractInfo")
    public CommonResult<Long> saveContractInfo(@RequestBody ContractInfoDTO contractInfoDto) throws BusinessException {
        // 保存时不校验
        Long id = contractInfoService.saveContractInfo(contractInfoDto);
        return CommonResult.success(id);
    }

    @ApiOperation("发起合同评审-提交")
    @PostMapping("/submitContractInfo")
    public CommonResult<Long> submitContractInfo(@RequestBody ContractInfoDTO contractInfoDto) throws BusinessException {
        // 提交时需要校验
        ValidationResult result = this.validator.validate(contractInfoDto);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        Long id = contractInfoService.submitReviews(contractInfoDto);
        return CommonResult.success(id);
    }

    @ApiOperation("销售公司-待处理评审-查询")
    @PostMapping("/getPendingReviews")
    public PageDataResult<SaleHomePendingReviewsVO> getPendingReviews(@RequestBody SaleHomePendingParam saleHomePendingParam) throws BusinessException {
        ValidationResult result = this.validator.validate(saleHomePendingParam);

        String userId = RequestContextHolder.getUserId();
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getPendingContractInfo(saleHomePendingParam);

        // 手动转换，避免类型不匹配问题
        List<SaleHomePendingReviewsVO> res = new ArrayList<>();
        for (ContractInfoDomain domain : pageDataInfo.getRows()) {
            SaleHomePendingReviewsVO vo = new SaleHomePendingReviewsVO();

            // 手动设置可能有类型不匹配的字段
            vo.setId(domain.getId() != null ? domain.getId().toString() : null);
            vo.setIsHead(domain.getIsHead() != null ? domain.getIsHead().byteValue() : null);

            // 使用BeanUtil复制其他字段（包括createTime、updateTime）
            BeanUtil.copyProperties(domain, vo, "id", "isHead");

            // 判断是否需要归档（标准科审核通过返回销售的订单）
            boolean needArchive = contractInfoService.canSalesFinalArchive(domain.getId());
            vo.setNeedArchive(needArchive);

            // 设置评审状态文本
            vo.setReviewStatusText(getReviewStatusText(domain.getReviewStatus()));

            res.add(vo);
        }

        // 同时处理norm和specification字段
        List<ContractInfoDomain> domains = pageDataInfo.getRows();
        for (int i = 0; i < res.size() && i < domains.size(); i++) {
            try {
                SaleHomePendingReviewsVO vo = res.get(i);
                ContractInfoDomain domain = domains.get(i);

                // 处理norm字段
                String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                vo.setNorm(split);

                // 格式化规格显示名称
                String displaySpecification = formatSpecificationDisplay(domain);
                vo.setSpecification(displaySpecification);
            } catch (Exception e) {
                // 如果处理单个记录失败，记录日志但不影响其他记录
                log.warn("Failed to process specification display for record {}: {}", i, e.getMessage());
                // 确保norm字段仍然被设置
                if (i < res.size()) {
                    SaleHomePendingReviewsVO vo = res.get(i);
                    String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                    vo.setNorm(split);
                }
            }
        }
        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("销售公司-已发送评审-查询")
    @PostMapping("/getSentReviews")
    public PageDataResult<SaleHomeSentReviewsVO> getSentReviews(@RequestBody SaleHomeSentParam saleHomeSentParam) throws BusinessException {
        ValidationResult result = this.validator.validate(saleHomeSentParam);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getSentContractInfo(saleHomeSentParam);
        List<SaleHomeSentReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeSentReviewsVO.class);

        // 处理norm和specification字段
        List<ContractInfoDomain> domains = pageDataInfo.getRows();
        for (int i = 0; i < res.size() && i < domains.size(); i++) {
            try {
                SaleHomeSentReviewsVO vo = res.get(i);
                ContractInfoDomain domain = domains.get(i);

                // 处理norm字段
                String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                vo.setNorm(split);

                // 格式化规格显示名称
                String displaySpecification = formatSpecificationDisplay(domain);
                vo.setSpecification(displaySpecification);

                // 设置评审状态文本
                vo.setReviewStatusText(getReviewStatusText(domain.getReviewStatus()));
            } catch (Exception e) {
                // 如果处理单个记录失败，记录日志但不影响其他记录
                log.warn("Failed to process specification display for record {}: {}", i, e.getMessage());
                // 确保norm字段仍然被设置
                if (i < res.size()) {
                    SaleHomeSentReviewsVO vo = res.get(i);
                    String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                    vo.setNorm(split);
                }
            }
        }
        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("销售公司-历史评审-查询")
    @PostMapping("/getHistoricalReviews")
    public PageDataResult<SaleHomeHistoricalReviewsVO> getHistoricalReviews(@RequestBody SaleHomeHistoricalParam saleHomeHistoricalParam) throws BusinessException {
        ValidationResult result = this.validator.validate(saleHomeHistoricalParam);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(saleHomeHistoricalParam);
        List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);

        // 处理norm、specification字段和评审意见
        List<ContractInfoDomain> domains = pageDataInfo.getRows();
        for (int i = 0; i < res.size() && i < domains.size(); i++) {
            try {
                SaleHomeHistoricalReviewsVO vo = res.get(i);
                ContractInfoDomain domain = domains.get(i);

                // 处理norm字段
                String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                vo.setNorm(split);

                // 格式化规格显示名称
                String displaySpecification = formatSpecificationDisplay(domain);
                vo.setSpecification(displaySpecification);

                // 设置评审状态文本
                vo.setReviewStatusText(getReviewStatusText(domain.getReviewStatus()));

                // 查询科室主任的评审意见
                ReviewComment reviewComment = reviewCommentService.getReviewOpinionById(vo.getId());
                if (reviewComment != null) {
                    vo.setReceivingState(reviewComment.getReceivingState());
                    vo.setReceivingStateText(getReceivingStateText(reviewComment.getReceivingState()));
                    vo.setReceivingRemark(reviewComment.getReceivingRemark());
                    vo.setCostCalculation(reviewComment.getCostCalculation());
                    vo.setAssess(reviewComment.getAssess());
                    vo.setAssessText(getAssessText(reviewComment.getAssess()));
                    vo.setIsMakeReview(reviewComment.getIsMake());
                    vo.setIsMakeReviewText(getIsMakeText(reviewComment.getIsMake()));
                    vo.setOutsourcingStatusReview(reviewComment.getOutsourcingStatus());
                    vo.setIsCostByChange(reviewComment.getIsCostByChange());
                    vo.setIsCostByChangeText(getIsCostByChangeText(reviewComment.getIsCostByChange()));
                }
            } catch (Exception e) {
                // 如果处理单个记录失败，记录日志但不影响其他记录
                log.warn("Failed to process specification display for record {}: {}", i, e.getMessage());
                // 确保norm字段仍然被设置
                if (i < res.size()) {
                    SaleHomeHistoricalReviewsVO vo = res.get(i);
                    String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                    vo.setNorm(split);
                }
            }
        }
        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("销售公司-历史评审-下载")
    @PostMapping("/download")
    public ResponseEntity<byte[]> download(@RequestBody SaleHomeHistoricalParam saleHomeHistoricalParam) throws BusinessException, IOException {

        // 获取要下载的合同ID列表
        List<String> contractIds = getContractIds(saleHomeHistoricalParam);

        if (contractIds.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "没有找到要下载的数据");
        }

        if (contractIds.size() == 1) {
            // 单文件下载：返回Word文档
            return downloadSingleWord(contractIds.get(0));
        } else {
            // 批量下载：返回ZIP压缩包
            return downloadMultipleWordsAsZip(contractIds);
        }
    }

    @ApiOperation("销售公司-历史评审-预览")
    @PostMapping("/preview")
    public CommonResult<String> preview(@RequestBody SaleHomeHistoricalParam saleHomeHistoricalParam, javax.servlet.http.HttpServletRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理评审单预览请求, 请求参数: {}", saleHomeHistoricalParam);

        // 1. 获取合同ID
        List<String> contractIds = getContractIds(saleHomeHistoricalParam);
        if (contractIds.isEmpty() || contractIds.size() > 1) {
            return CommonResult.failed("预览功能仅支持单个合同，请选择一个合同进行预览");
        }
        String contractId = contractIds.get(0);

        try {
            // 2. 生成Word文件字节流
            byte[] wordBytes = generateSingleWordBytes(contractId);

            // 获取合同编号用于文件名
            SaleHomeHistoricalParam param = new SaleHomeHistoricalParam();
            param.setContractInfoId(contractId);
            param.setPage(1);
            param.setCurrent(1);
            PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(param);
            String contractCode = pageDataInfo.getRows().isEmpty() ? contractId : pageDataInfo.getRows().get(0).getCode();
            String tempFileName = "评审单_" + contractCode + "_" + System.currentTimeMillis() + ".docx";

            // 3. 上传到MinIO
            log.info("开始上传评审单到MinIO, 文件名: {}", tempFileName);
            try (java.io.InputStream inputStream = new java.io.ByteArrayInputStream(wordBytes)) {
                minioClient.putObject(
                        io.minio.PutObjectArgs.builder()
                                .bucket(minioConfig.getBucketName())
                                .object(tempFileName)
                                .stream(inputStream, wordBytes.length, -1)
                                .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                                .build()
                );
            }
            log.info("评审单上传MinIO成功");

            // 4. 构建kkFileView预览URL
            String token = request.getHeader(HttpHeaders.AUTHORIZATION);
            String serverUrl = getServerUrl();
            String fileDownloadUrl = serverUrl + "/file/download/" + token + "/" + tempFileName;

            String base64Url = java.util.Base64.getEncoder().encodeToString(fileDownloadUrl.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            long endTime = System.currentTimeMillis();
            log.info("评审单预览请求处理完成, 耗时: {}ms", (endTime - startTime));
            String previewUrl = filePreviewUrl + "?url=" + base64Url;
            log.info("最终预览URL: {}", previewUrl);

            // 5. 返回预览URL
            return CommonResult.success(previewUrl);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("预览评审单处理失败, 耗时: {}ms, 合同ID: {}", (endTime - startTime), contractId, e);
            return CommonResult.failed("预览失败：" + e.getMessage());
        }
    }

    /**
     * 获取要下载的合同ID列表
     */
    private List<String> getContractIds(SaleHomeHistoricalParam param) {
        List<String> contractIds = new ArrayList<>();

        // 优先使用contractInfoIds数组
        if (param.getContractInfoIds() != null && !param.getContractInfoIds().isEmpty()) {
            contractIds.addAll(param.getContractInfoIds());
        }
        // 兼容旧的contractInfoId字段
        else if (StringUtils.isNotBlank(param.getContractInfoId())) {
            contractIds.add(param.getContractInfoId());
        }

        return contractIds;
    }

    /**
     * 单文件Word下载
     */
    private ResponseEntity<byte[]> downloadSingleWord(String contractInfoId) throws BusinessException, IOException {
        // 创建查询参数，只查询指定的合同
        SaleHomeHistoricalParam param = new SaleHomeHistoricalParam();
        param.setContractInfoId(contractInfoId);  // 设置contractInfoId字段，会被复制到value4
        param.setPage(1);      // 设置页码（下载时不需要分页，设置默认值）
        param.setCurrent(1);   // 设置当前页（下载时不需要分页，设置默认值）

        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }

        log.info("=== 查询历史合同信息调试 ===");
        log.info("查询参数 - 合同ID: {}, contractInfoId: {}, page: {}, current: {}", contractInfoId, param.getContractInfoId(), param.getPage(), param.getCurrent());

        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(param);
        List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);

        log.info("查询结果 - 总记录数: {}, 返回记录数: {}", pageDataInfo.getTotal(), res.size());
        if (!res.isEmpty()) {
            SaleHomeHistoricalReviewsVO first = res.get(0);
            log.info("第一条记录 - ID: {}, Code: {}, 规格: {}", first.getId(), first.getCode(), first.getSpecification());
        }

        if (res.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "没有找到合同信息：" + contractInfoId);
        }

        // 只处理第一条记录（应该只有一条）
        SaleHomeHistoricalReviewsVO re = res.get(0);

        // 使用提取的公共方法生成数据
        Map<String, String> data = generateContractData(re, contractInfoId);

        // 调试：打印数据Map的内容
        log.info("=== downloadSingleWord 生成的数据Map ===");
        for (Map.Entry<String, String> entry : data.entrySet()) {
            log.info("Key: [{}] -> Value: [{}]", entry.getKey(), entry.getValue());
        }

        for (Map.Entry<String, String> stringStringEntry : data.entrySet()) {
            if (stringStringEntry.getValue() == null) {
                stringStringEntry.setValue(" ");
            }
        }

            // 使用配置文件中的模板路径
            String templatePath = templateBasePath + reviewTemplateName;

            // 调试：打印文件路径
            log.info("=== downloadSingleWord 文件路径调试 ===");
            log.info("模板文件路径: {}", templatePath);
            log.info("模板文件是否存在: {}", Files.exists(Paths.get(templatePath)));

            // 创建临时文件用于生成Word（使用更唯一的文件名避免缓存问题）
            String uniqueId = System.currentTimeMillis() + "_" + System.nanoTime() + "_" + (int)(Math.random() * 10000);
            String tempWordPath = System.getProperty("java.io.tmpdir") + "contract_review_" + contractInfoId + "_" + uniqueId + ".docx";
            log.info("临时文件路径: {}", tempWordPath);

            // 替换模板内容
            String processedWordPath = WordTableTextReplacer.replaceTextInTable(templatePath, tempWordPath, data);
            log.info("处理后文件路径: {}", processedWordPath);
            log.info("处理后文件是否存在: {}", Files.exists(Paths.get(processedWordPath)));

            // 读取处理后的Word文件
            byte[] wordBytes = Files.readAllBytes(Paths.get(processedWordPath));

            // 清理临时文件
            Files.deleteIfExists(Paths.get(processedWordPath));

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            // 使用合同编号+合同ID确保文件名唯一
            String originalFilename = re.getCode() + "_" + contractInfoId + "_合同评审记录.docx";  // 原始文件名（包含中文）
            String encodedFilename = URLEncoder.encode(originalFilename, "UTF-8");  // URL编码后的文件名

            log.info("单选下载文件名: 合同编号={}, 合同ID={}, 文件名={}", re.getCode(), contractInfoId, originalFilename);

            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            // 使用RFC 5987标准设置文件名，支持中文
            headers.add("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);
            headers.add("download-filename", encodedFilename);  // 添加自定义文件名头（编码后），方便前端获取
            headers.add("Access-Control-Expose-Headers", "download-filename, content-disposition");  // 允许前端访问自定义响应头

            // 调试：打印响应头信息
            System.out.println("=== 响应头调试 ===");
            System.out.println("原始文件名: " + originalFilename);
            System.out.println("编码后文件名: " + encodedFilename);
            System.out.println("Content-Type: " + headers.getContentType());
            System.out.println("Content-Disposition: " + headers.getContentDisposition());
            System.out.println("download-filename: " + headers.get("download-filename"));
            System.out.println("注意：download-filename使用编码后的文件名，前端需要解码");
            System.out.println("Access-Control-Expose-Headers: " + headers.get("Access-Control-Expose-Headers"));

            // 返回Word文件流
            return ResponseEntity.ok()
                .headers(headers)
                .body(wordBytes);
    }

    /**
     * 批量Word下载（ZIP压缩包）
     */
    private ResponseEntity<byte[]> downloadMultipleWordsAsZip(List<String> contractIds) throws BusinessException, IOException {
        if (contractIds.size() > 10) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "单次最多只能下载10个文件");
        }

        ByteArrayOutputStream zipStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(zipStream);

        try {
            for (String contractId : contractIds) {
                try {
                    // 生成单个Word的字节数组
                    byte[] wordBytes = generateSingleWordBytes(contractId);

                    // 获取合同编号用于文件名
                    SaleHomeHistoricalParam param = new SaleHomeHistoricalParam();
                    param.setContractInfoId(contractId);
                    param.setPage(1);
                    param.setCurrent(1);
                    PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(param);
                    List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);
                    String contractCode = res.isEmpty() ? contractId : res.get(0).getCode();

                    // 生成唯一的文件名，使用合同编号+合同ID确保唯一性
                    String fileName = contractCode + "_" + contractId + "_合同评审记录.docx";

                    // 添加到ZIP
                    ZipEntry entry = new ZipEntry(fileName);
                    zip.putNextEntry(entry);
                    zip.write(wordBytes);
                    zip.closeEntry();

                    log.info("成功添加文件到ZIP: {}, 合同ID: {}", fileName, contractId);
                } catch (Exception e) {
                    log.warn("生成合同Word失败，合同ID: {}, 错误: {}", contractId, e.getMessage());
                    // 继续处理其他文件，不中断整个流程
                }
            }
        } finally {
            zip.close();
        }

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        // 使用与单文件下载相同的文件名处理方式
        String originalFilename = "contract_reviews_" + System.currentTimeMillis() + ".zip";
        try {
            String encodedFilename = URLEncoder.encode(originalFilename, "UTF-8");
            headers.add("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);
            headers.add("download-filename", encodedFilename);
            headers.add("Access-Control-Expose-Headers", "download-filename, content-disposition");
        } catch (UnsupportedEncodingException e) {
            // 如果编码失败，使用原始文件名
            headers.setContentDisposition(ContentDisposition.attachment()
                .filename(originalFilename)
                .build());
        }

        return ResponseEntity.ok()
            .headers(headers)
            .body(zipStream.toByteArray());
    }

    /**
     * 生成单个Word的字节数组（用于批量下载）
     */
    private byte[] generateSingleWordBytes(String contractInfoId) throws Exception {
        // 创建查询参数
        SaleHomeHistoricalParam param = new SaleHomeHistoricalParam();
        param.setContractInfoId(contractInfoId);  // 设置contractInfoId字段，会被复制到value4
        param.setPage(1);      // 设置页码（下载时不需要分页，设置默认值）
        param.setCurrent(1);   // 设置当前页（下载时不需要分页，设置默认值）

        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(param);
        List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);

        if (res.isEmpty()) {
            throw new RuntimeException("没有找到合同信息：" + contractInfoId);
        }

        // 处理数据（复用单文件下载的逻辑）
        SaleHomeHistoricalReviewsVO re = res.get(0);
        Map<String, String> data = generateContractData(re, contractInfoId);

        // 生成Word文档
        String templatePath = templateBasePath + reviewTemplateName;
        // 使用更唯一的文件名，包含纳秒时间戳和随机数，避免缓存问题
        String uniqueId = System.currentTimeMillis() + "_" + System.nanoTime() + "_" + (int)(Math.random() * 10000);
        String tempWordPath = System.getProperty("java.io.tmpdir") + "contract_review_" + contractInfoId + "_" + uniqueId + ".docx";

        log.info("=== Word文档生成调试 ===");
        log.info("模板路径: {}", templatePath);
        log.info("临时文件路径: {}", tempWordPath);
        log.info("数据Map大小: {}", data.size());

        // 打印所有数据Map的内容
        log.info("=== 完整数据Map内容 ===");
        for (Map.Entry<String, String> entry : data.entrySet()) {
            log.info("Key: [{}] -> Value: [{}]", entry.getKey(), entry.getValue());
        }

        // 确保临时目录存在
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }

        String processedWordPath = WordTableTextReplacer.replaceTextInTable(templatePath, tempWordPath, data);
        log.info("处理后文件路径: {}", processedWordPath);

        // 读取处理后的Word文件
        byte[] wordBytes = Files.readAllBytes(Paths.get(processedWordPath));

        // 清理临时文件
        Files.deleteIfExists(Paths.get(processedWordPath));

        return wordBytes;
    }

    /**
     * 生成合同数据Map（提取公共逻辑）
     */
    private Map<String, String> generateContractData(SaleHomeHistoricalReviewsVO re, String contractInfoId) {
        Map<String, String> data = new HashMap<>();

        // 将String类型的contractInfoId转换为Long类型，用于数据库查询
        Long contractId = Long.parseLong(contractInfoId);

        String[] split = re.getValue1() != null ? re.getValue1().split(",") : new String[0];
        re.setNorm(split);

        // 处理规格名称：从拼接的规格名称中提取基础规格名称
        String specification = re.getSpecification(); // 默认使用原始规格名称
        String baseSpecification = specification;

        log.info("=== 规格处理调试 ===");
        log.info("合同ID: {}", contractInfoId);
        log.info("原始规格名称(可能包含备注): [{}]", specification);

        // 如果规格名称包含空格，说明可能是"规格名称 备注"的格式，提取基础规格名称
        if (StringUtils.isNotBlank(specification) && specification.contains(" ")) {
            baseSpecification = specification.split(" ")[0]; // 取空格前的部分作为基础规格名称
            log.info("提取的基础规格名称: [{}]", baseSpecification);

            // 用基础规格名称查询规格基础表
            LambdaQueryWrapper<SpecificationBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(SpecificationBase::getSpecification, baseSpecification);
            SpecificationBase byId = specificationBaseService.getOne(lambdaQueryWrapper);

            if (byId != null) {
                log.info("查询成功，找到规格基础信息: ID={}, specification=[{}]", byId.getId(), byId.getSpecification());
                // 使用完整的规格名称（包含备注）
                specification = re.getSpecification();
            } else {
                log.warn("未找到基础规格信息: [{}]，使用原始规格名称", baseSpecification);
                specification = re.getSpecification();
            }
        } else {
            log.info("规格名称不包含空格，直接使用: [{}]", specification);
        }

        log.info("最终使用的规格名称: [{}]", specification);
        log.info("=== 规格处理调试结束 ===");

        String getValue1 = re.getValue1() != null ? re.getValue1() + "mm" : " ";
        String getValue2 = re.getValue2() != null ? "x" + re.getValue2() + "mm" : " ";
        String getValue3 = re.getValue3() != null ? "x" + re.getValue3() + "mm" : " ";
        data.put("{steelGrade}", re.getSteelGradeName());
        data.put("{phone}", re.getCustomerPhone());
        data.put("{name}", re.getCustomerName());
        data.put("{steelType1}", "汽车钢".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelType2}", "航空、航天及甲类".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelType3}", "核电".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelType4}", "API".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelType5}", "其它".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelSpecification}", specification);
        data.put("{button}", getValue1 + getValue2 + getValue3);
        data.put("{specialRequirements}", re.getSpecialRequirements() != null ? re.getSpecialRequirements() : "");
        data.put("{processingPurpose}", re.getProcessingPurpose() != null ? re.getProcessingPurpose() : "");
        data.put("{standard}", re.getStandardName() != null ? re.getStandardName() : "");
        data.put("{technicalStandard}", re.getTechnicalStandardName() != null ? "技术条件：" + re.getTechnicalStandardName() : "技术条件：");
        data.put("{authorName}", re.getAuthorName() != null ? re.getAuthorName() : "");
        data.put("{salesmanName}", re.getSalesmanName() != null ? re.getSalesmanName() : "");

        // 继续处理其他数据...
        ContractInfo byId1 = contractInfoService.getById(contractId);
        ReviewComment reviewComment = reviewCommentService.getReviewOpinionById(contractId.toString());

        List<ReviewInfo> reviewInfos = reviewInfoService.getReviewInfoById(String.valueOf(reviewComment.getId()));

        // 提取特定角色的评审信息
        String stdAssessor = "";
        String stdAssessDate = "";
        String techCenterDirector = "";

        // 从ProcessFlow表获取标准室审核人员信息
        List<ProcessFlow> processFlows = processFlowService.list(
            new LambdaQueryWrapper<ProcessFlow>()
                .eq(ProcessFlow::getContractInfoId, contractId)
                .orderByDesc(ProcessFlow::getCreateTime)
        );

        log.info("=== 调试标准室审核人员 ===");
        log.info("合同ID: {}, 找到ProcessFlow记录数: {}", contractId, processFlows.size());

        for (ProcessFlow processFlow : processFlows) {
            log.info("ProcessFlow - ID: {}, Step: {}, CreateUser: {}, CreateTime: {}",
                processFlow.getId(), processFlow.getCurrentStep(), processFlow.getCreateUser(), processFlow.getCreateTime());

            // 标准科审核通过步骤，记录了标准室规范性审核人员
            log.info("比较步骤: {} vs REEVALUATION({}), 相等: {}",
                processFlow.getCurrentStep(), StepEnum.REEVALUATION, StepEnum.REEVALUATION.equals(processFlow.getCurrentStep()));

            if (StepEnum.REEVALUATION.equals(processFlow.getCurrentStep())) {
                log.info("找到标准科审核通过步骤，CreateUser: {}", processFlow.getCreateUser());
                Employee employee = userService.getById(processFlow.getCreateUser());
                if (employee != null) {
                    stdAssessor = employee.getNickname() != null ? employee.getNickname() : employee.getUsername();
                    log.info("标准室审核人员: {} (nickname: {}, username: {})", stdAssessor, employee.getNickname(), employee.getUsername());
                } else {
                    log.warn("未找到员工信息，CreateUser: {}", processFlow.getCreateUser());
                }
            }

            // 技术中心主任通过步骤，记录了技术中心大主任
            log.info("比较步骤: {} vs RETURN_PINGSHEN({}), 相等: {}",
                processFlow.getCurrentStep(), StepEnum.RETURN_PINGSHEN, StepEnum.RETURN_PINGSHEN.equals(processFlow.getCurrentStep()));

            if (StepEnum.RETURN_PINGSHEN.equals(processFlow.getCurrentStep())) {
                log.info("找到技术中心主任通过步骤，CreateUser: {}", processFlow.getCreateUser());
                Employee employee = userService.getById(processFlow.getCreateUser());
                if (employee != null) {
                    log.info("技术中心主任员工信息 - ID: {}, Name: {}, Nickname: {}, Isdirector: {}",
                        employee.getId(), employee.getUsername(), employee.getNickname(), employee.getIsdirector());

                    techCenterDirector = employee.getNickname() != null ? employee.getNickname() : employee.getUsername();
                    log.info("设置技术中心大主任: {}, isdirector: {}", techCenterDirector, employee.getIsdirector());
                }
            }
        }

        log.info("最终标准室审核人员: {}", stdAssessor);

        log.info("最终技术中心大主任: {}", techCenterDirector);

        // 获取标准室审核日期
        if (byId1.getAuditId() != null) {
            Auditt audit = auditService.getById(byId1.getAuditId());
            if (audit != null && audit.getAuditTime() != null) {
                stdAssessDate = audit.getAuditTime().format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
            }
        }

        // 处理评审意见（最多5个）
        for (int i = 0; i < 5; i++) {
            int index = i + 1;
            if (i < reviewInfos.size()) {
                ReviewInfo reviewInfo = reviewInfos.get(i);
                String comment = reviewInfo.getCommentModified() != null ? reviewInfo.getCommentModified() : reviewInfo.getReviewComment();
                String assessorUsername = reviewInfo.getAssessor();
                String department = reviewInfo.getDepartment();

                // 将员工username转换为员工姓名
                String assessorName = assessorUsername;
                if (assessorUsername != null && !assessorUsername.isEmpty()) {
                    try {
                        Employee employee = employeeMapper.selectOne(new LambdaQueryWrapper<Employee>()
                            .eq(Employee::getUsername, assessorUsername));
                        if (employee != null && employee.getNickname() != null) {
                            assessorName = employee.getNickname();
                        }
                    } catch (Exception e) {
                        log.warn("Failed to convert assessor username {} to name: {}", assessorUsername, e.getMessage());
                    }
                }

                data.put("B" + index, department != null ? department : " ");
                data.put("C" + index, assessorName != null ? assessorName : " ");
                data.put("A" + index, comment != null ? comment : " ");
            } else {
                data.put("B" + index, " ");
                data.put("C" + index, " ");
                data.put("A" + index, " ");
            }
        }

        // 处理评审结果数据
        ReviewCommentInfoDtoDto reviewCommentInfoDto = new ReviewCommentInfoDtoDto();
        BeanUtil.copyProperties(reviewComment, reviewCommentInfoDto);
        Integer isMake = reviewCommentInfoDto.getIsMake();
        Integer assess = reviewCommentInfoDto.getAssess();
        Long isCostByChange = reviewCommentInfoDto.getIsCostByChange();
        String receivingRemark = reviewCommentInfoDto.getReceivingRemark();

        data.put("{receivingRemark}", receivingRemark != null ? receivingRemark : "");
        data.put("f1", isCostByChange == 1 ? "√" : " ");
        data.put("f2", isCostByChange == 0 ? "√" : " ");
        data.put("r1", isMake == 1 ? "√" : " ");
        data.put("r2", isMake == 0 ? "√" : " ");
        data.put("{assess1}", assess == 0 ? "√" : " ");
        data.put("{assess2}", assess == 1 ? "√" : " ");
        data.put("{assess3}", assess == 2 ? "√" : " ");

        // 处理最终意见数据
        if (byId1.getOverallOpinionId() != null) {
            String remark = overallOpinionService.getById(byId1.getOverallOpinionId()).getRemark();
            data.put("{remark}", remark != null ? remark : "");
        }

        if (byId1.getFinalOpinionId() != null) {
            LambdaQueryWrapper<FinalOpinion> eq = new LambdaQueryWrapper<>();
            eq.eq(FinalOpinion::getId, byId1.getFinalOpinionId());
            FinalOpinion one = finalOpinionService.getOne(eq);
            if (one != null) {
                Integer isConclude = one.getIsConclude();
                Integer isOrderGood = one.getIsOrderGood();
                Integer productType = one.getProductType();
                String annotatedContent = one.getAnnotatedContent();

                data.put("{isOrderGood1}", isOrderGood == 0 ? "√" : " ");
                data.put("{isOrderGood2}", isOrderGood == 1 ? "√" : " ");
                data.put("{productType1}", productType == 0 ? "√" : " ");
                data.put("{productType2}", productType == 1 ? "√" : " ");
                data.put("uutt", isConclude == 0 ? "√" : " ");
                data.put("sssss", isConclude == 1 ? "√" : " ");
                data.put("{annotatedContent}", annotatedContent != null ? annotatedContent : "");
            }
        }

        // 添加时间和ID
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        data.put("{createTime}", byId1.getCreateTime() != null ? byId1.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy.MM.dd")) : "");
        data.put("{id}", re.getCode()); // 使用F开头的编号

        // 填充特定角色的信息
        log.info("=== 数据填充调试 ===");
        log.info("stdAssessor: [{}]", stdAssessor);
        log.info("stdAssessDate: [{}]", stdAssessDate);
        log.info("techCenterDirector: [{}]", techCenterDirector);

        // 添加冶炼方法字段
        String smeltingProcess = byId1.getSmeltingProcess();
        log.info("smeltingProcess: [{}]", smeltingProcess);

        // 使用正确的占位符格式（{中文名称}）
        data.put("{标准室审核人员}", stdAssessor);
        data.put("{技术中心大主任}", techCenterDirector);
        data.put("{冶炼方法}", smeltingProcess != null ? smeltingProcess : "");

        // 保留原有的英文占位符，以防万一
        data.put("{stdAssessor}", stdAssessor);
        data.put("{stdAssessDate}", stdAssessDate);
        data.put("{techCenterDirector}", techCenterDirector);
        data.put("{smeltingMethod}", smeltingProcess != null ? smeltingProcess : "");
        data.put("{smeltingProcess}", smeltingProcess != null ? smeltingProcess : "");

        log.info("=== 数据Map内容 ===");
        log.info("data.get(\"{标准室审核人员}\"): [{}]", data.get("{标准室审核人员}"));
        log.info("data.get(\"{技术中心大主任}\"): [{}]", data.get("{技术中心大主任}"));
        log.info("data.get(\"{冶炼方法}\"): [{}]", data.get("{冶炼方法}"));
        log.info("data.get(\"{stdAssessor}\"): [{}]", data.get("{stdAssessor}"));
        log.info("data.get(\"{techCenterDirector}\"): [{}]", data.get("{techCenterDirector}"));
        log.info("data.get(\"{smeltingMethod}\"): [{}]", data.get("{smeltingMethod}"));

        // 处理空值
        for (Map.Entry<String, String> entry : data.entrySet()) {
            if (entry.getValue() == null) {
                entry.setValue(" ");
            }
        }

        return data;
    }

    @ApiOperation("发起合同评审-查询技术条件")
    @GetMapping("/getTechStandardList")
    public CommonResult<List<TechnicalStandardBase>> getTechStandardList(@ApiParam(value = "{\"technicalStandardName\":\"\"}") @RequestParam(required = false) Map<String, String> param) {
        String keyword = param.get("technicalStandardName");

        String userId = RequestContextHolder.getUserId();
        LambdaQueryWrapper<TechnicalStandardBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(TechnicalStandardBase::getTechnicalStandardName, keyword);
        }
        return CommonResult.success(technicalStandardBaseService.list(queryWrapper));
    }


    @ApiOperation("发起合同评审-查询钢种")
    @PostMapping("/getSteelGradeListByName")
    public CommonResult<List<SteelGradeBase>> getSteelGradeListByName(@ApiParam(value = "{\"steelGradeName\":\"\"}") @RequestBody(required = false) Map<String, String> param) throws BusinessException {
        String steelGradeName = "";
        String userId = RequestContextHolder.getUserId();
        if (param != null && !param.isEmpty()) {
            steelGradeName = param.get("steelGradeName");
        }
        List<SteelGradeBase> list = steelGradeBaseService.getSteelGradeListByName(steelGradeName);
        return CommonResult.success(list);
    }

    @ApiOperation("发起合同评审-查询钢类")
    @PostMapping("/getSteelTypeListByName")
    public CommonResult<List<SteelTypeBase>> getSteelTypeListByName(@ApiParam(value = "{\"steelTypeName\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String steelTypeName = "";
        if (param != null && !param.isEmpty()) {
            steelTypeName = param.get("steelTypeName");
        }
        List<SteelTypeBase> list = steelTypeBaseService.getSteelTypeListByName(steelTypeName.trim());
        return CommonResult.success(list);
    }

    @ApiOperation("发起合同评审-待处理事项")
    @PostMapping("/getItemBase")
    public CommonResult<List<ItemBase>> getItemBase(@ApiParam(value = "{\"itemName\":\"\"}") @RequestBody(required = false) Map<String, String> map) {

        String itemName = "";
        if (map != null && !map.isEmpty()) {
            itemName = map.get("itemName");
        }
        List<ItemBase> list = itemBaseService.getItemBase(itemName.trim());
        return CommonResult.success(list);
    }


    @ApiOperation("发起合同评审-查询顾客")
    @PostMapping("/getCustomerListByName")
    public CommonResult<List<CustomerInfoVO>> getCustomerListByName(@ApiParam(value = "{\"customerName\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        if (map == null || map.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数为空");
        }
        String customerName = map.get("customerName");
        List<CustomerInfo> customerInfoList = customerInfoService.getCustomerInfoListByName(customerName.trim());
        List<CustomerInfoVO> customerInfoVOList = BeanUtil.copyToList(customerInfoList, CustomerInfoVO.class);
        return CommonResult.success(customerInfoVOList);
    }

    @ApiOperation("发起合同评审-查询标准")
    @GetMapping("/getStandardList")
    public CommonResult<List<StandardBase>> getStandardList(@ApiParam(value = "{\"standard_name\":\"\"}") @RequestParam(required = false) Map<String, String> param) {
        String keyword = param.get("standard_name");
        LambdaQueryWrapper<StandardBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(StandardBase::getStandardName, keyword);
        }
        return CommonResult.success(standardBaseService.list(queryWrapper));
    }

    @ApiOperation("发起合同评审-查询交货状态")
    @PostMapping("/getDeliveryStatusList")
    public CommonResult<List<DeliveryStatusBase>> getDeliveryStatusList(@ApiParam(value = "{\"delivery_status\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String keyword = param.get("delivery_status");
        LambdaQueryWrapper<DeliveryStatusBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(DeliveryStatusBase::getDeliveryStatus, keyword);
        }
        return CommonResult.success(deliveryStatusBaseService.list(queryWrapper));
    }

    @ApiOperation("发起合同评审-查询加工用途")
    @PostMapping("/getProcessingPurposeList")
    public CommonResult<List<ProcessingPurposeBase>> getProcPurposeList(@ApiParam(value = "{\"processing_purpose\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String keyword = param.get("processing_purpose");
        LambdaQueryWrapper<ProcessingPurposeBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(ProcessingPurposeBase::getProcessingPurpose, keyword);
        }
        return CommonResult.success(processingPurposeBaseService.list(queryWrapper));
    }

    @ApiOperation("发起合同评审-查询规格")
    @PostMapping("/getSpecificationBaseList")
    public CommonResult<List<SpecificationBaseVo>> getSpecificationBaseList(
            @ApiParam(value = "{\"specification\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String specification = param == null ? null : param.get("specification");
        List<SpecificationBase> list = specificationBaseService.getSpecificationBaseListByKeyword(specification);
        List<SpecificationBaseVo> specificationBaseVos = new ArrayList<>();
        for (SpecificationBase specificationBaseVo : list) {
            String[] strings = specificationBaseVo.getButton() != null ? specificationBaseVo.getButton().split(",") : null;
            SpecificationBaseVo specificationBaseVo1 = new SpecificationBaseVo();
            specificationBaseVo1.setSpecification(specificationBaseVo.getSpecification());
            specificationBaseVo1.setId(specificationBaseVo.getId());
            specificationBaseVo1.setButton(strings);
            specificationBaseVo1.setSpecificationNote(specificationBaseVo.getSpecificationNote());
            // 不设置displayName，保持原有功能
            specificationBaseVos.add(specificationBaseVo1);
        }
        return CommonResult.success(specificationBaseVos);
    }

    @ApiOperation("待处理评审-核定外委业务-查询外委厂商")
    @PostMapping("/getOutsourcingList")
    public CommonResult<List<Outsourcing>> getOutsourcingList(@ApiParam(value = "{\"outsourcingName\":\"\",\"outsourcingPhone\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        if (map == null || map.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数不能为空");
        }
        Outsourcing outsourcing = BeanUtil.mapToBean(map, Outsourcing.class, true);
        if (Objects.isNull(outsourcing)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数不能为空");
        }
        List<Outsourcing> list = outsourcingService.list(new LambdaQueryWrapper<Outsourcing>()
                .like(!(Objects.isNull(outsourcing.getOutsourcingName()) || outsourcing.getOutsourcingName().isEmpty()),
                        Outsourcing::getOutsourcingName, outsourcing.getOutsourcingName())
                .like(!(Objects.isNull(outsourcing.getOutsourcingPhone()) || outsourcing.getOutsourcingPhone().isEmpty()),
                        Outsourcing::getOutsourcingPhone, outsourcing.getOutsourcingPhone()));
        return CommonResult.success(list);
    }

    @ApiOperation("查询当前状态")
    @PostMapping("/getStatus")
    public CommonResult<List<StatusBase>> getStatus(@ApiParam(value = "{\"statusName\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String keyword = param.get("statusName");
        LambdaQueryWrapper<StatusBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(StatusBase::getStatusName, keyword);
        }
        return CommonResult.success(statusBaseMapper.selectList(queryWrapper));
    }

    @ApiOperation("查询审核类型下拉")
    @GetMapping("/getReviewType")
    public CommonResult<List<ReviewTypeBase>> getReviewType() {
        return CommonResult.success(reviewTypeService.list());
    }

    /**
     * 格式化规格显示名称
     * @param domain 合同信息域对象
     * @return 拼接后的显示名称
     */
    private String formatSpecificationDisplay(ContractInfoDomain domain) {
        // 安全检查
        if (domain == null || domain.getSpecification() == null) {
            return "";
        }

        // 从已拼接的specification中提取原始规格名称和备注
        String originalSpec = domain.getSpecification();
        String specName = originalSpec;
        String specNote = "";

        // 尝试分离规格名称和备注（格式：规格名称 备注）
        int spaceIndex = originalSpec.indexOf(' ');
        if (spaceIndex > 0) {
            specName = originalSpec.substring(0, spaceIndex);
            specNote = originalSpec.substring(spaceIndex + 1);
        }

        StringBuilder result = new StringBuilder(specName);

        // 获取具体尺寸信息和维度信息
        if (domain.getValue1() != null && !domain.getValue1().isEmpty() && domain.getSteelSpecificationId() != null) {
            try {
                // 根据steelSpecificationId查询SpecificationInfo，然后获取SpecificationBase的button信息
                SpecificationInfo specInfo = specificationInfoService.getById(domain.getSteelSpecificationId());
                if (specInfo != null && specInfo.getSpecificationId() != null) {
                    SpecificationBase specBase = specificationBaseService.getById(specInfo.getSpecificationId());
                    if (specBase != null && specBase.getButton() != null && !specBase.getButton().isEmpty()) {
                        // 使用完整的拼接逻辑
                        String[] dimensions = specBase.getButton().split(",");
                        String[] values = domain.getValue1().split(",");

                        result.append(":");
                        for (int i = 0; i < Math.min(dimensions.length, values.length); i++) {
                            if (i > 0) {
                                result.append("*");  // 添加维度分隔符
                            }
                            result.append(dimensions[i]).append(values[i]).append("mm");
                        }
                    } else {
                        // 如果没有维度信息，使用简单拼接
                        result.append(" ").append(domain.getValue1());
                    }
                } else {
                    // 如果查询不到规格信息，使用简单拼接
                    result.append(" ").append(domain.getValue1());
                }
            } catch (Exception e) {
                // 如果查询失败，使用简单拼接
                log.warn("Failed to format specification display for domain: {}", domain.getId(), e);
                result.append(" ").append(domain.getValue1());
            }
        }

        // 添加规格备注
        if (specNote != null && !specNote.isEmpty()) {
            result.append("（").append(specNote).append("）");
        }

        return result.toString();
    }

    @ApiOperation("获取发起人下拉框选项")
    @GetMapping("/getSubmitUserOptions")
    public CommonResult<List<String>> getSubmitUserOptions(@RequestParam(required = false) String name) {
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<Employee>()
            .select(Employee::getNickname)
            .orderBy(true, true, Employee::getNickname);

        // 如果传入了名字，进行模糊查询
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(Employee::getNickname, name);
        }

        List<Employee> employees = userService.list(queryWrapper);

        List<String> names = employees.stream()
            .map(Employee::getNickname)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        return CommonResult.success(names);
    }

    /**
     * 获取接单状态文字描述
     * @param receivingState 接单状态
     * @return 状态描述
     */
    private String getReceivingStateText(Integer receivingState) {
        if (receivingState == null) return null;
        switch (receivingState) {
            case 0: return "拒单";
            case 1: return "接单";
            case 2: return "条件接单";
            default: return "未知状态";
        }
    }

    /**
     * 获取风险等级评估文字描述
     * @param assess 风险等级评估
     * @return 评估描述
     */
    private String getAssessText(Integer assess) {
        if (assess == null) return null;
        switch (assess) {
            case 0: return "低风险";
            case 1: return "中风险";
            case 2: return "高风险";
            default: return "未知风险";
        }
    }

    /**
     * 获取首试制文字描述
     * @param isMake 首试制
     * @return 首试制描述
     */
    private String getIsMakeText(Integer isMake) {
        if (isMake == null) return null;
        switch (isMake) {
            case 0: return "否";
            case 1: return "是";
            default: return "未知";
        }
    }

    /**
     * 获取是否引起成本变化文字描述
     * @param isCostByChange 是否引起成本变化
     * @return 成本变化描述
     */
    private String getIsCostByChangeText(Long isCostByChange) {
        if (isCostByChange == null) return null;
        switch (isCostByChange.intValue()) {
            case 0: return "否";
            case 1: return "是";
            default: return "未知";
        }
    }

    @ApiOperation("销售公司-历史评审-导出Excel")
    @PostMapping("/exportHistoricalReviews")
    public void exportHistoricalReviews(@RequestBody Map<String, List<String>> request, HttpServletResponse response) throws BusinessException, IOException {
        List<String> contractIds = request.get("contractInfoIds");

        if (contractIds == null || contractIds.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "请选择要导出的合同");
        }

        if (contractIds.size() > 1000) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "单次最多导出1000条记录");
        }

        log.info("开始导出Excel，合同数量: {}", contractIds.size());

        // 1. 查询数据
        List<SaleHomeHistoricalReviewsVO> dataList = new ArrayList<>();
        for (String contractId : contractIds) {
            try {
                SaleHomeHistoricalParam param = new SaleHomeHistoricalParam();
                param.setContractInfoId(contractId);
                param.setPage(1);
                param.setCurrent(1);

                PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(param);
                List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);

                if (!res.isEmpty()) {
                    SaleHomeHistoricalReviewsVO vo = res.get(0);
                    // 处理规格显示名称
                    try {
                        String displaySpecification = formatSpecificationDisplay(pageDataInfo.getRows().get(0));
                        vo.setSpecification(displaySpecification);
                    } catch (Exception e) {
                        log.warn("处理规格显示失败，合同ID: {}, 错误: {}", contractId, e.getMessage());
                    }

                    // 查询并设置评审意见相关字段
                    try {
                        log.info("开始查询评审意见，合同ID: {}, VO.ID: {}", contractId, vo.getId());
                        ReviewComment reviewComment = reviewCommentService.getReviewOpinionById(vo.getId());
                        if (reviewComment != null) {
                            log.info("找到评审意见，receivingState: {}", reviewComment.getReceivingState());
                            vo.setReceivingState(reviewComment.getReceivingState());
                            String receivingStateText = getReceivingStateText(reviewComment.getReceivingState());
                            vo.setReceivingStateText(receivingStateText);
                            log.info("设置评审结论文本: {}", receivingStateText);
                            vo.setReceivingRemark(reviewComment.getReceivingRemark());
                            vo.setCostCalculation(reviewComment.getCostCalculation());
                            vo.setAssess(reviewComment.getAssess());
                            vo.setAssessText(getAssessText(reviewComment.getAssess()));
                            vo.setIsMakeReview(reviewComment.getIsMake());
                            vo.setIsMakeReviewText(getIsMakeText(reviewComment.getIsMake()));
                            vo.setOutsourcingStatusReview(reviewComment.getOutsourcingStatus());
                            vo.setIsCostByChange(reviewComment.getIsCostByChange());
                            vo.setIsCostByChangeText(getIsCostByChangeText(reviewComment.getIsCostByChange()));
                        } else {
                            log.warn("未找到评审意见，合同ID: {}, VO.ID: {}", contractId, vo.getId());
                        }

                        // 验证设置结果
                        log.info("最终VO中的评审结论: {}", vo.getReceivingStateText());
                    } catch (Exception e) {
                        log.warn("查询评审意见失败，合同ID: {}, 错误: {}", contractId, e.getMessage());
                    }

                    dataList.add(vo);
                } else {
                    log.warn("未找到合同数据，合同ID: {}", contractId);
                }
            } catch (Exception e) {
                log.warn("查询合同数据失败，合同ID: {}, 错误: {}", contractId, e.getMessage());
            }
        }

        if (dataList.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "没有找到可导出的数据");
        }

        // 2. 生成Excel
        generateExcelFile(dataList, response);

        log.info("Excel导出完成，实际导出记录数: {}", dataList.size());
    }

    /**
     * 生成Excel文件
     */
    private void generateExcelFile(List<SaleHomeHistoricalReviewsVO> dataList, HttpServletResponse response) throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("合同评审记录");

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "编号", "发起人", "顾客名称", "顾客联系方式", "钢类", "钢种", "标准", "规格", "数量", "交货状态", "加工用途", "提交时间", "评审结论"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet.createRow(i + 1);
            SaleHomeHistoricalReviewsVO data = dataList.get(i);

            // 序号
            Cell cell0 = row.createCell(0);
            cell0.setCellValue(i + 1);
            cell0.setCellStyle(dataStyle);

            // 编号
            Cell cell1 = row.createCell(1);
            cell1.setCellValue(data.getCode() != null ? data.getCode() : "");
            cell1.setCellStyle(dataStyle);

            // 发起人
            Cell cell2 = row.createCell(2);
            cell2.setCellValue(data.getSubmitUser() != null ? data.getSubmitUser() : "");
            cell2.setCellStyle(dataStyle);

            // 顾客名称
            Cell cell3 = row.createCell(3);
            cell3.setCellValue(data.getCustomerName() != null ? data.getCustomerName() : "");
            cell3.setCellStyle(dataStyle);

            // 顾客联系方式
            Cell cell4 = row.createCell(4);
            cell4.setCellValue(data.getCustomerPhone() != null ? data.getCustomerPhone() : "");
            cell4.setCellStyle(dataStyle);

            // 钢类
            Cell cell5 = row.createCell(5);
            cell5.setCellValue(data.getSteelTypeName() != null ? data.getSteelTypeName() : "");
            cell5.setCellStyle(dataStyle);

            // 钢种
            Cell cell6 = row.createCell(6);
            cell6.setCellValue(data.getSteelGradeName() != null ? data.getSteelGradeName() : "");
            cell6.setCellStyle(dataStyle);

            // 标准
            Cell cell7 = row.createCell(7);
            cell7.setCellValue(data.getStandardName() != null ? data.getStandardName() : "-");
            cell7.setCellStyle(dataStyle);

            // 规格
            Cell cell8 = row.createCell(8);
            cell8.setCellValue(data.getSpecification() != null ? data.getSpecification() : "");
            cell8.setCellStyle(dataStyle);

            // 数量
            Cell cell9 = row.createCell(9);
            String quantity = "";
            if (data.getSteelNumber() != null) {
                quantity = data.getSteelNumber().toString();
                if (data.getSteelNumberUnit() != null) {
                    quantity += data.getSteelNumberUnit().getText();
                }
            }
            cell9.setCellValue(quantity);
            cell9.setCellStyle(dataStyle);

            // 交货状态
            Cell cell10 = row.createCell(10);
            cell10.setCellValue(data.getDeliveryStatus() != null ? data.getDeliveryStatus() : "");
            cell10.setCellStyle(dataStyle);

            // 加工用途
            Cell cell11 = row.createCell(11);
            cell11.setCellValue(data.getProcessingPurpose() != null ? data.getProcessingPurpose() : "");
            cell11.setCellStyle(dataStyle);

            // 提交时间
            Cell cell12 = row.createCell(12);
            if (data.getSubmitTime() != null) {
                cell12.setCellValue(data.getSubmitTime());
            } else {
                cell12.setCellValue("");
            }
            cell12.setCellStyle(dataStyle);

            // 评审结论
            Cell cell13 = row.createCell(13);
            String receivingStateText = data.getReceivingStateText();
            log.info("Excel第{}行，合同ID: {}, 评审结论: {}", i+1, data.getId(), receivingStateText);
            cell13.setCellValue(receivingStateText != null ? receivingStateText : "-");
            cell13.setCellStyle(dataStyle);
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
            // 设置最小列宽
            int columnWidth = sheet.getColumnWidth(i);
            if (columnWidth < 2000) {
                sheet.setColumnWidth(i, 2000);
            }
            // 设置最大列宽
            if (columnWidth > 8000) {
                sheet.setColumnWidth(i, 8000);
            }
        }

        // 设置响应头
        String fileName = "合同评审记录_" + DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(java.time.LocalDateTime.now()) + ".xlsx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8");

        // 重置响应，清除之前可能设置的Content-Type
        response.reset();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        // 输出文件
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
        } finally {
            workbook.close();
        }
    }

    private String getServerUrl() {
        if (serverUrl == null) {
            // 优先使用配置的外部地址
            if (StringUtils.isNotBlank(configuredServerUrl)) {
                serverUrl = configuredServerUrl;
                log.info("使用配置的服务器URL: {}", serverUrl);
            } else {
                // 降级到自动获取IP
                try {
                    String ip = java.net.InetAddress.getLocalHost().getHostAddress();
                    String port = env.getProperty("server.port");
                    serverUrl = "http://" + ip + ":" + port;
                    log.info("自动获取服务器URL: {}", serverUrl);
                } catch (Exception e) {
                    log.error("获取服务器URL失败", e);
                    // 使用默认值
                    serverUrl = "http://localhost:" + env.getProperty("server.port", "8089");
                }
            }
        }
        return serverUrl;
    }

    /**
     * 销售最终归档
     */
    @ApiOperation("销售最终归档")
    @PostMapping("/finalArchive")
    public CommonResult<Boolean> finalArchive(@ApiParam(value = "合同ID") @RequestParam Long contractInfoId) throws BusinessException {
        try {
            // 参数验证
            if (contractInfoId == null) {
                throw new BusinessException(ResultCode.VALIDATE_FAILED, "合同ID不能为空");
            }

            log.info("销售最终归档，合同ID: {}", contractInfoId);

            // 检查当前流程状态是否为"标准科审核通过返回销售"
            boolean canArchive = contractInfoService.canSalesFinalArchive(contractInfoId);
            if (!canArchive) {
                throw new BusinessException(ResultCode.VALIDATE_FAILED, "当前状态不允许归档操作");
            }

            // 执行最终归档
            boolean success = contractInfoService.salesFinalArchive(contractInfoId);
            if (success) {
                log.info("销售最终归档成功，合同ID: {}", contractInfoId);
                return CommonResult.success(true);
            } else {
                throw new BusinessException(ResultCode.UNKNOWN_ERROR, "归档失败");
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("销售最终归档失败", e);
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "归档失败：" + e.getMessage());
        }
    }

    /**
     * 根据评审状态值获取对应的文本描述
     */
    private String getReviewStatusText(Integer reviewStatus) {
        if (reviewStatus == null) {
            return "";
        }
        switch (reviewStatus) {
            case 1:
                return "草稿状态";
            case 2:
                return "评审中状态";
            case 3:
                return "被驳回状态";
            case 4:
                return "核定外委状态";
            case 5:
                return "待归档状态";
            case 6:
                return "已归档状态";
            default:
                return "未知状态";
        }
    }
}
